// Mock data for development when API is not accessible
const mockPrayerData = {
  Date: "2-Jul",
  Imsaku: "2:39",
  <PERSON><PERSON>: "2:59",
  <PERSON><PERSON><PERSON>: "4:54",
  <PERSON><PERSON>: "12:43",
  <PERSON><PERSON><PERSON>: "16:47",
  <PERSON><PERSON><PERSON><PERSON>: "20:24",
  <PERSON><PERSON><PERSON>: "22:29",
  Festat: "Viti i Ri Hixhri 1447|26-Jun|<PERSON><PERSON> e <PERSON>|5-Jul|Nata e Mevludit|3-Sep|Mevludi|4-Sep|Nata e Regaibit|25-Dec",
  "Shenime\r": "Sot është një ditë e veçantë për lutje\r",
  data_e_formatuar: "Wednesday, July 2, 2025"
};

export const fetchPrayerTimes = async () => {
  try {
    // Try to fetch from the actual API first
    const response = await fetch('https://prayer-api.takvimi.workers.dev/api/vaktet', {
      mode: 'cors',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      return data.data;
    } else {
      throw new Error('API response not ok');
    }
  } catch (error) {
    console.warn('API fetch failed, using mock data:', error.message);
    // Return mock data when API fails
    return mockPrayerData;
  }
};

export const fetchPrayerTimesForDate = async (date) => {
  try {
    const response = await fetch(`https://prayer-api.takvimi.workers.dev/api/vaktet?date=${date}`, {
      mode: 'cors',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      return data.data;
    } else {
      throw new Error('API response not ok');
    }
  } catch (error) {
    console.warn('API fetch failed, using mock data:', error.message);
    // Return mock data when API fails
    return mockPrayerData;
  }
};

export const parseFestivals = (festivalString) => {
  if (!festivalString || festivalString.trim() === '') {
    return [];
  }
  
  const festivals = [];
  const parts = festivalString.split('|');
  
  for (let i = 0; i < parts.length; i += 2) {
    if (i + 1 < parts.length) {
      festivals.push({
        name: parts[i],
        date: parts[i + 1]
      });
    }
  }
  
  return festivals;
};

