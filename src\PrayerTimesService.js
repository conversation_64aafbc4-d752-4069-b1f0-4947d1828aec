// Generate mock data based on the requested date
const generateMockPrayerData = (requestedDate = null) => {
  // Use current date if no specific date requested
  const targetDate = requestedDate ? new Date(requestedDate + '-2025') : new Date();

  // Format date for API format (e.g., "2-Jul")
  const day = targetDate.getDate();
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                     'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const month = monthNames[targetDate.getMonth()];
  const dateStr = `${day}-${month}`;

  // Format full date string
  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  const monthFullNames = ['January', 'February', 'March', 'April', 'May', 'June',
                         'July', 'August', 'September', 'October', 'November', 'December'];
  const dayName = dayNames[targetDate.getDay()];
  const monthFull = monthFullNames[targetDate.getMonth()];
  const year = targetDate.getFullYear();
  const formattedDate = `${dayName}, ${monthFull} ${day}, ${year}`;

  // Generate different prayer times based on date (simulate seasonal variation)
  const dayOfYear = Math.floor((targetDate - new Date(targetDate.getFullYear(), 0, 0)) / 86400000);
  const seasonalOffset = Math.sin((dayOfYear / 365) * 2 * Math.PI) * 60; // ±60 minutes variation

  const baseTimes = {
    Imsaku: 159, // 2:39 in minutes
    Sabahu: 179, // 2:59
    Lindja: 294, // 4:54
    Dreka: 763, // 12:43
    Ikindia: 1007, // 16:47
    Akshami: 1224, // 20:24
    Jacia: 1349 // 22:29
  };

  const formatTime = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return `${hours}:${mins.toString().padStart(2, '0')}`;
  };

  // Generate dynamic Shenime based on date
  const shenimeOptions = [
    "Sot është një ditë e bekuar për lutje dhe dhikër",
    "Kujtoni të lexoni Kuranin dhe të bëni dua",
    "Një ditë e mirë për vepra të mira dhe bamirësi",
    "Mos harroni namazin në kohë dhe lutjet e përditshme",
    "Ditë e favorshme për kërkimin e faljes dhe mëshirës",
    "Koha e mirë për reflektim dhe meditim spiritual"
  ];

  const shenimeIndex = dayOfYear % shenimeOptions.length;
  const selectedShenime = shenimeOptions[shenimeIndex];

  return {
    Date: dateStr,
    Imsaku: formatTime(baseTimes.Imsaku + seasonalOffset * 0.3),
    Sabahu: formatTime(baseTimes.Sabahu + seasonalOffset * 0.3),
    Lindja: formatTime(baseTimes.Lindja + seasonalOffset * 0.8),
    Dreka: formatTime(baseTimes.Dreka), // Dreka stays relatively stable
    Ikindia: formatTime(baseTimes.Ikindia + seasonalOffset * 0.5),
    Akshami: formatTime(baseTimes.Akshami + seasonalOffset * 0.8),
    Jacia: formatTime(baseTimes.Jacia + seasonalOffset * 0.4),
    Festat: "Nata e Regaibit|2-Jan|Nata e Miraxhit|26-Jan|Nata e Beratit|13-Feb|Nata e Ramazanit|28-Feb|Dita 1 e Ramazanit|1-Mar|Nata e Kadrit|26-Mar|Nata e Fiter-Bajramit|29-Mar|Dita 1 e Fiter-Bajramit|30-Mar|Nata e Kurban-Bajramit|5-Jun|Dita 1 e Kurban-Bajramit|6-Jun|Viti i Ri Hixhri 1447|26-Jun|Dita e Ashures|5-Jul|Nata e Mevludit|3-Sep|Mevludi|4-Sep|Nata e Regaibit|25-Dec",
    "Shenime\r": selectedShenime,
    Shenime: selectedShenime,
    data_e_formatuar: formattedDate
  };
};

export const fetchPrayerTimes = async () => {
  try {
    // Try to fetch from the actual API first
    const response = await fetch('https://prayer-api.takvimi.workers.dev/api/vaktet', {
      mode: 'cors',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });

    if (response.ok) {
      const data = await response.json();
      return data.data;
    } else {
      throw new Error('API response not ok');
    }
  } catch (error) {
    console.warn('API fetch failed, using mock data:', error.message);
    // Return mock data when API fails - use current date
    return generateMockPrayerData();
  }
};

export const fetchPrayerTimesForDate = async (date) => {
  try {
    const response = await fetch(`https://prayer-api.takvimi.workers.dev/api/vaktet?date=${date}`, {
      mode: 'cors',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });

    if (response.ok) {
      const data = await response.json();
      return data.data;
    } else {
      throw new Error('API response not ok');
    }
  } catch (error) {
    console.warn('API fetch failed, using mock data for date:', date, error.message);
    // Return mock data when API fails - use the requested date
    return generateMockPrayerData(date);
  }
};

export const parseFestivals = (festivalString) => {
  if (!festivalString || festivalString.trim() === '') {
    return [];
  }
  
  const festivals = [];
  const parts = festivalString.split('|');
  
  for (let i = 0; i < parts.length; i += 2) {
    if (i + 1 < parts.length) {
      festivals.push({
        name: parts[i],
        date: parts[i + 1]
      });
    }
  }
  
  return festivals;
};

