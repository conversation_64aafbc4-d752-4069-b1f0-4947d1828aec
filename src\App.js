import React, { useState, useEffect } from 'react';
import './App.css';
import { fetchPrayerTimes, fetchPrayerTimesForDate, parseFestivals } from './PrayerTimesService';
import { formatDateToAlbanian, formatDateForAPI, calculateRemainingTime, calculateDaylightTime } from './utils/dateUtils';

function App() {
  const [prayerTimes, setPrayerTimes] = useState(null);
  const [currentPrayer, setCurrentPrayer] = useState(null);
  const [nextPrayer, setNextPrayer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [remainingTime, setRemainingTime] = useState('00:00:00');
  const [festivals, setFestivals] = useState([]);
  const [shenime, setShenime] = useState('');

  const prayerNames = {
    'Imsaku': 'Imsaku (Sahur)',
    'Sabahu': '<PERSON>u (Fajr)',
    'Lindja': 'Lindja (Sunrise)',
    'Dreka': 'Dreka (Dhuhr)',
    'Ikindia': 'Ikindia (Asr)',
    'Akshami': 'Akshami (Maghrib)',
    'Jacia': 'Jacia (Isha)'
  };

  const prayerDescriptions = {
    'Imsaku': 'Koha e sahur-it',
    'Sabahu': 'Namazi i mëngjesit',
    'Lindja': 'Lindja e diellit',
    'Dreka': 'Namazi i drekës',
    'Ikindia': 'Namazi i pasdites',
    'Akshami': 'Namazi i mbrëmjes',
    'Jacia': 'Namazi i natës'
  };

  const loadPrayerTimes = async (date = null) => {
    try {
      setLoading(true);
      setError(null);
      
      let data;
      if (date) {
        const dateStr = formatDateForAPI(date);
        data = await fetchPrayerTimesForDate(dateStr);
      } else {
        data = await fetchPrayerTimes();
      }
      
      setPrayerTimes(data);
      
      // Parse festivals
      const festivalList = parseFestivals(data.Festat);
      setFestivals(festivalList);
      
      // Set Shenime
      const shenimeText = data["Shenime\r"] || data.Shenime || '';
      setShenime(shenimeText.trim().replace(/\r/g, ''));
      
    } catch (err) {
      setError('Gabim në ngarkimin e të dhënave');
      console.error('Error loading prayer times:', err);
    } finally {
      setLoading(false);
    }
  };

  const getCurrentAndNextPrayer = () => {
    if (!prayerTimes) return;

    // Always use current time in Tirana timezone for prayer calculation
    const now = new Date();
    const tiranaTime = new Date(now.toLocaleString("en-US", {timeZone: "Europe/Tirane"}));
    const currentHour = tiranaTime.getHours();
    const currentMinute = tiranaTime.getMinutes();
    const currentTimeInMinutes = currentHour * 60 + currentMinute;
    
    const prayers = [
      { name: 'Imsaku', time: prayerTimes.Imsaku },
      { name: 'Sabahu', time: prayerTimes.Sabahu },
      { name: 'Lindja', time: prayerTimes.Lindja },
      { name: 'Dreka', time: prayerTimes.Dreka },
      { name: 'Ikindia', time: prayerTimes.Ikindia },
      { name: 'Akshami', time: prayerTimes.Akshami },
      { name: 'Jacia', time: prayerTimes.Jacia }
    ];

    // Convert prayer times to minutes for easier comparison
    const prayersWithMinutes = prayers.map(prayer => {
      const [hours, minutes] = prayer.time.split(':').map(Number);
      return {
        ...prayer,
        timeInMinutes: hours * 60 + minutes
      };
    });

    let current = null;
    let next = null;

    // Find current and next prayer
    for (let i = 0; i < prayersWithMinutes.length; i++) {
      const prayerTimeInMinutes = prayersWithMinutes[i].timeInMinutes;
      
      if (currentTimeInMinutes >= prayerTimeInMinutes) {
        current = prayersWithMinutes[i];
      } else {
        next = prayersWithMinutes[i];
        break;
      }
    }

    // If no next prayer found (after last prayer of the day), next prayer is tomorrow's first prayer
    if (!next) {
      next = prayersWithMinutes[0];
    }

    setCurrentPrayer(current);
    setNextPrayer(next);
  };

  const updateRemainingTime = () => {
    if (!nextPrayer) return;
    
    const now = new Date();
    const tiranaTime = new Date(now.toLocaleString("en-US", {timeZone: "Europe/Tirane"}));
    const remaining = calculateRemainingTime(nextPrayer.time, tiranaTime);
    setRemainingTime(remaining);
  };

  useEffect(() => {
    loadPrayerTimes();
  }, []);

  useEffect(() => {
    getCurrentAndNextPrayer();
  }, [prayerTimes]);

  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date();
      setCurrentTime(now);
      getCurrentAndNextPrayer();
      updateRemainingTime();
    }, 1000);

    return () => clearInterval(timer);
  }, [prayerTimes, nextPrayer]);

  const handleRefresh = () => {
    loadPrayerTimes();
  };

  const handleDateChange = (event) => {
    const newDate = new Date(event.target.value);
    setSelectedDate(newDate);
    loadPrayerTimes(newDate);
  };

  const formatFestivalDate = (dateStr) => {
    // Convert date format like "26-Jun" to Albanian format
    const [day, monthAbbr] = dateStr.split('-');
    const monthMap = {
      'Jan': 'janar', 'Feb': 'shkurt', 'Mar': 'mars', 'Apr': 'prill',
      'May': 'maj', 'Jun': 'qershor', 'Jul': 'korrik', 'Aug': 'gusht',
      'Sep': 'shtator', 'Oct': 'tetor', 'Nov': 'nëntor', 'Dec': 'dhjetor'
    };
    
    const month = monthMap[monthAbbr] || monthAbbr;
    const date = new Date(2025, Object.keys(monthMap).indexOf(monthAbbr), parseInt(day));
    const dayName = ['E diel', 'E hënë', 'E martë', 'E mërkurë', 'E enjte', 'E premte', 'E shtunë'][date.getDay()];
    
    return `${dayName}, ${day} ${month} 2025`;
  };

  const getDaylightTime = () => {
    if (!prayerTimes) return '';
    return calculateDaylightTime(prayerTimes.Lindja, prayerTimes.Akshami);
  };

  const getAlbanianDate = () => {
    if (prayerTimes && prayerTimes.data_e_formatuar) {
      // Parse the English date and convert to Albanian
      const englishDate = new Date(prayerTimes.data_e_formatuar);
      return formatDateToAlbanian(englishDate);
    }
    return formatDateToAlbanian(selectedDate);
  };

  if (loading) {
    return (
      <div className="App">
        <div className="loading">
          <div className="spinner"></div>
          <p>Duke ngarkuar kohët e namazit...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="App">
        <div className="error">
          <h2>Gabim në ngarkim</h2>
          <p>{error}</p>
          <button onClick={handleRefresh} className="refresh-btn">
            Provo përsëri
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="App">
      <header className="App-header">
        <h1>Kohët e Namazit</h1>
        <p className="date">{getAlbanianDate()}</p>
        <div className="time-and-date-picker">
          <div className="current-time">
            {new Date().toLocaleTimeString('sq-AL', { 
              timeZone: 'Europe/Tirane',
              hour: '2-digit', 
              minute: '2-digit',
              second: '2-digit'
            })} (Tirana)
          </div>
          <input 
            type="date" 
            value={selectedDate.toISOString().split('T')[0]}
            onChange={handleDateChange}
            className="date-picker"
          />
        </div>
        <button onClick={handleRefresh} className="refresh-btn">
          🔄 Rifresko
        </button>
      </header>

      {currentPrayer && (
        <div className="current-prayer-info">
          <h3>Namazi aktual: {prayerNames[currentPrayer.name]}</h3>
        </div>
      )}

      {nextPrayer && (
        <div className="next-prayer-info">
          <h3>Namazi i ardhshëm: {prayerNames[nextPrayer.name]} në {nextPrayer.time} edhe {remainingTime}</h3>
        </div>
      )}

      {shenime && (
        <div className="shenime-info">
          <strong>Shënime:</strong> {shenime}
        </div>
      )}

      <main>
        {Object.entries(prayerNames).map(([key, displayName]) => {
          const time = prayerTimes[key];
          const isCurrent = currentPrayer?.name === key;
          const isNext = nextPrayer?.name === key;
          
          return (
            <div 
              key={key} 
              className={`prayer-card ${isCurrent ? 'current' : ''} ${isNext ? 'next' : ''}`}
            >
              <div className="prayer-icon">🕌</div>
              <h2>{displayName}</h2>
              <p className="prayer-time">{time}</p>
              <p className="prayer-description">{prayerDescriptions[key]}</p>
              {isCurrent && <span className="status-badge current-badge">Aktual</span>}
              {isNext && <span className="status-badge next-badge">I ardhshëm</span>}
            </div>
          );
        })}
      </main>

      {prayerTimes && (
        <div className="daylight-info">
          <h3>⏰ Koha e Diellit</h3>
          <p>Nga Lindja ({prayerTimes.Lindja}) deri në Akshami ({prayerTimes.Akshami}): <strong>{getDaylightTime()}</strong></p>
        </div>
      )}

      {festivals.length > 0 && (
        <div className="festivals-section">
          <h3>🌙 Festat Islame 2025</h3>
          <div className="festivals-list">
            {festivals.map((festival, index) => (
              <div key={index} className="festival-item">
                <span className="festival-name">{festival.name}</span>
                <span className="festival-date">{formatFestivalDate(festival.date)}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default App;

