:root {
  --primary-color: #194b7e; /* <PERSON> Green */
  --secondary-color: #DAA520; /* Goldenrod */
  --background-color: #F0F2F5;
  --card-background: #FFFFFF;
  --text-color: #333333;
  --highlight-color: #FFD700; /* Gold for highlighting */
  --current-color: #228B22; /* Forest Green */
  --next-color: #FF8C00; /* Dark Orange */
  --shadow-color: rgba(0, 0, 0, 0.1);
}

* {
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #E8F5E8 0%, #F0F8FF 100%);
  color: var(--text-color);
  margin: 0;
  padding: 20px;
  min-height: 100vh;
}

.App {
  max-width: 1200px;
  margin: 0 auto;
  background-color: var(--background-color);
  border-radius: 15px;
  box-shadow: 0 8px 32px var(--shadow-color);
  overflow: hidden;
}

/* Header Styles */
.App-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, #228B22 100%);
  color: white;
  padding: 30px;
  text-align: center;
  position: relative;
}

.App-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="islamic" patternUnits="userSpaceOnUse" width="20" height="20"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23islamic)"/></svg>') repeat;
  opacity: 0.3;
}

.App-header > * {
  position: relative;
  z-index: 1;
}

.App-header h1 {
  margin: 0 0 10px 0;
  font-size: 2.8em;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.date {
  font-size: 1.2em;
  margin: 10px 0;
  opacity: 0.9;
}

.current-time {
  font-size: 1.8em;
  font-weight: bold;
  margin: 15px 0;
  background: rgba(255, 255, 255, 0.2);
  padding: 10px 20px;
  border-radius: 25px;
  display: inline-block;
  backdrop-filter: blur(10px);
}

.refresh-btn {
  background: var(--secondary-color);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 1em;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 15px;
  box-shadow: 0 4px 15px rgba(218, 165, 32, 0.3);
}

.refresh-btn:hover {
  background: #B8860B;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(218, 165, 32, 0.4);
}

/* Prayer Info Sections */
.current-prayer-info,
.next-prayer-info {
  padding: 20px;
  text-align: center;
  font-weight: 600;
}

.current-prayer-info {
  background: linear-gradient(135deg, rgba(34, 139, 34, 0.1) 0%, rgba(34, 139, 34, 0.05) 100%);
  color: var(--current-color);
  border-bottom: 3px solid var(--current-color);
}

.next-prayer-info {
  background: linear-gradient(135deg, rgba(255, 140, 0, 0.1) 0%, rgba(255, 140, 0, 0.05) 100%);
  color: var(--next-color);
  border-bottom: 3px solid var(--next-color);
}

.current-prayer-info h3,
.next-prayer-info h3 {
  margin: 0;
  font-size: 1.3em;
}

/* Main Content */
main {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
  padding: 30px;
  background: var(--background-color);
}

/* Prayer Cards */
.prayer-card {
  background: linear-gradient(145deg, var(--card-background), #FAFAFA);
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 6px 20px var(--shadow-color);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.prayer-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  opacity: 0.7;
}

.prayer-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
  border-color: var(--primary-color);
}

.prayer-card.current {
  border-color: var(--current-color);
  background: linear-gradient(145deg, #F0FFF0, #F8FFF8);
  box-shadow: 0 8px 25px rgba(34, 139, 34, 0.2);
}

.prayer-card.next {
  border-color: var(--next-color);
  background: linear-gradient(145deg, #FFF8DC, #FFFAF0);
  box-shadow: 0 8px 25px rgba(255, 140, 0, 0.2);
}

.prayer-icon {
  font-size: 2.5em;
  margin-bottom: 15px;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
}

.prayer-card h2 {
  color: var(--primary-color);
  font-size: 1.6em;
  margin: 0 0 10px 0;
  font-weight: 700;
}

.prayer-time {
  font-size: 2.5em;
  font-weight: bold;
  color: var(--secondary-color);
  margin: 10px 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.prayer-description {
  font-size: 1em;
  color: #666;
  margin: 10px 0 0 0;
  font-style: italic;
}

.status-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8em;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.current-badge {
  background: var(--current-color);
  color: white;
  box-shadow: 0 2px 8px rgba(34, 139, 34, 0.3);
}

.next-badge {
  background: var(--next-color);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 140, 0, 0.3);
}

/* Loading and Error States */
.loading,
.error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  text-align: center;
  padding: 40px;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading p {
  font-size: 1.2em;
  color: var(--primary-color);
  font-weight: 600;
}

.error h2 {
  color: #dc3545;
  margin-bottom: 15px;
}

.error p {
  color: #666;
  margin-bottom: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  body {
    padding: 10px;
  }

  .App-header {
    padding: 20px;
  }

  .App-header h1 {
    font-size: 2.2em;
  }

  .current-time {
    font-size: 1.5em;
    padding: 8px 16px;
  }

  main {
    grid-template-columns: 1fr;
    padding: 20px;
    gap: 20px;
  }

  .prayer-card {
    padding: 20px;
  }

  .prayer-time {
    font-size: 2.2em;
  }

  .current-prayer-info h3,
  .next-prayer-info h3 {
    font-size: 1.1em;
  }
}

@media (max-width: 480px) {
  .App-header h1 {
    font-size: 1.8em;
  }

  .current-time {
    font-size: 1.3em;
  }

  .prayer-card {
    padding: 15px;
  }

  .prayer-card h2 {
    font-size: 1.4em;
  }

  .prayer-time {
    font-size: 2em;
  }

  .prayer-icon {
    font-size: 2em;
  }

  main {
    padding: 15px;
  }
}

/* Animation for smooth transitions */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.prayer-card {
  animation: fadeIn 0.6s ease-out;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .prayer-card,
  .refresh-btn {
    transition: none;
  }
  
  .spinner {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --primary-color: #000080;
    --secondary-color: #8B4513;
    --text-color: #000000;
  }
}



/* Time and Date Picker Container */
.time-and-date-picker {
  display: flex;
  align-items: center;
  gap: 20px;
  margin: 15px 0;
  flex-wrap: wrap;
  justify-content: center;
}

.date-picker {
  padding: 10px 15px;
  border: 2px solid var(--accent-color);
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.9);
  color: var(--primary-color);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.date-picker:hover {
  background: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.date-picker:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.3);
}

/* Shenime Info */
.shenime-info {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  border: 2px solid #ffc107;
  border-radius: 15px;
  padding: 15px 20px;
  margin: 20px auto;
  max-width: 800px;
  text-align: center;
  color: #856404;
  font-size: 16px;
  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);
}

.shenime-info strong {
  color: #b8860b;
}

/* Date Info for non-today views */
.date-info {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  border: 2px solid #2196f3;
  border-radius: 15px;
  padding: 15px 20px;
  margin: 20px auto;
  max-width: 800px;
  text-align: center;
  color: #1565c0;
  font-size: 16px;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.2);
}

.date-info h3 {
  margin: 0;
  color: #0d47a1;
}

/* Daylight Info */
.daylight-info {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  border: 2px solid #2196f3;
  border-radius: 15px;
  padding: 20px;
  margin: 30px auto;
  max-width: 600px;
  text-align: center;
  color: #0d47a1;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.2);
}

.daylight-info h3 {
  margin: 0 0 10px 0;
  color: #1565c0;
  font-size: 20px;
}

.daylight-info p {
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
}

.daylight-info strong {
  color: #0d47a1;
  font-weight: 700;
}

/* Festivals Section */
.festivals-section {
  background: linear-gradient(135deg, #f3e5f5, #e1bee7);
  border: 2px solid #194b7e;
  border-radius: 15px;
  padding: 25px;
  margin: 30px auto;
  max-width: 800px;
  box-shadow: 0 4px 15px rgba(156, 39, 176, 0.2);
}

.festivals-section h3 {
  text-align: center;
  color: #194b7e;
  margin: 0 0 20px 0;
  font-size: 22px;
}

.festivals-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.festival-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  padding: 15px 20px;
  border-radius: 10px;
  border-left: 4px solid #194b7e;
  transition: all 0.3s ease;
}

.festival-item:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: translateX(5px);
  box-shadow: 0 2px 10px rgba(156, 39, 176, 0.15);
}

.festival-name {
  font-weight: 600;
  color: #4a148c;
  flex: 1;
}

.festival-date {
  color: #7b1fa2;
  font-size: 14px;
  font-weight: 500;
}

/* Responsive Design for New Elements */
@media (max-width: 768px) {
  .time-and-date-picker {
    flex-direction: column;
    gap: 15px;
  }
  
  .date-picker {
    width: 100%;
    max-width: 250px;
  }
  
  .festival-item {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
  
  .festival-name {
    margin-bottom: 5px;
  }
  
  .daylight-info,
  .festivals-section,
  .shenime-info {
    margin: 20px 10px;
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .festivals-section h3 {
    font-size: 18px;
  }
  
  .festival-item {
    padding: 12px 15px;
  }
  
  .daylight-info h3 {
    font-size: 18px;
  }
  
  .daylight-info p {
    font-size: 14px;
  }
}

