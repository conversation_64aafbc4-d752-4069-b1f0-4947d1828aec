{"version": 3, "file": "static/css/main.dd33f53a.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAKF,CAEA,KACE,uEAEF,CCZA,MACE,uBAAwB,CACxB,yBAA0B,CAC1B,0BAA2B,CAC3B,sBAA0B,CAC1B,iBAAqB,CACrB,sBAA0B,CAC1B,uBAAwB,CACxB,oBAAqB,CACrB,wBACF,CAEA,EACE,qBACF,CAEA,KAEE,kDAA6D,CAC7D,UAAwB,CAAxB,uBAAwB,CAFxB,qDAA4D,CAG5D,QAAS,CAET,gBAAiB,CADjB,YAEF,CAEA,KAGE,wBAAyC,CAAzC,wCAAyC,CACzC,kBAAmB,CACnB,+BAA0C,CAA1C,yCAA0C,CAH1C,aAAc,CADd,gBAAiB,CAKjB,eACF,CAGA,YACE,kDAA0E,CAA1E,sEAA0E,CAC1E,UAAY,CACZ,YAAa,CAEb,iBAAkB,CADlB,iBAEF,CAEA,mBAOE,kWAAsU,CADtU,QAAS,CALT,UAAW,CAGX,MAAO,CAIP,UAAY,CANZ,iBAAkB,CAGlB,OAAQ,CAFR,KAMF,CAEA,cACE,iBAAkB,CAClB,SACF,CAEA,eAEE,eAAgB,CAChB,eAAgB,CAFhB,eAAkB,CAGlB,iCACF,CAEA,MACE,eAAgB,CAChB,aAAc,CACd,UACF,CAEA,cAQE,kCAA2B,CAA3B,0BAA2B,CAJ3B,gBAAoC,CAEpC,kBAAmB,CACnB,oBAAqB,CANrB,eAAgB,CAChB,eAAiB,CACjB,aAAc,CAEd,iBAIF,CAEA,aACE,kBAAkC,CAAlC,iCAAkC,CAElC,WAAY,CAEZ,kBAAmB,CAMnB,+BAA8C,CAT9C,UAAY,CAMZ,cAAe,CAFf,aAAc,CACd,eAAgB,CAGhB,eAAgB,CANhB,iBAAkB,CAKlB,uBAGF,CAEA,mBACE,kBAAmB,CAEnB,+BAA8C,CAD9C,0BAEF,CAGA,uCAIE,eAAgB,CAFhB,YAAa,CACb,iBAEF,CAEA,qBACE,sDAA4F,CAE5F,+BAA6C,CAA7C,4CAA6C,CAD7C,aAA2B,CAA3B,0BAEF,CAEA,kBACE,sDAA4F,CAE5F,+BAA0C,CAA1C,yCAA0C,CAD1C,aAAwB,CAAxB,uBAEF,CAEA,6CAGE,eAAgB,CADhB,QAEF,CAGA,KAGE,aAAS,CAET,kBAAmC,CAAnC,kCAAmC,CAJnC,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,YAEF,CAGA,aASE,kBAAmB,CARnB,+CAAoE,CAApE,iEAAoE,CAUpE,sBAA6B,CAT7B,kBAAmB,CAEnB,+BAA0C,CAA1C,yCAA0C,CAE1C,YAAa,CACb,qBAAsB,CACtB,sBAAuB,CAKvB,eAAgB,CAVhB,YAAa,CASb,iBAAkB,CAFlB,iBAAkB,CALlB,uBASF,CAEA,oBAOE,iDAAgF,CAAhF,6EAAgF,CANhF,UAAW,CAKX,UAAW,CAFX,MAAO,CAIP,UAAY,CANZ,iBAAkB,CAGlB,OAAQ,CAFR,KAMF,CAEA,mBAGE,oBAAkC,CAAlC,iCAAkC,CADlC,gCAA2C,CAD3C,0BAGF,CAEA,qBAEE,kDAAqD,CADrD,oBAAkC,CAAlC,iCAAkC,CAElC,+BACF,CAEA,kBAEE,kDAAqD,CADrD,oBAA+B,CAA/B,8BAA+B,CAE/B,+BACF,CAEA,aAGE,8CAAmD,CAFnD,eAAgB,CAChB,kBAEF,CAEA,gBACE,aAA2B,CAA3B,0BAA2B,CAC3B,eAAgB,CAEhB,eAAgB,CADhB,eAEF,CAEA,aAGE,aAA6B,CAA7B,4BAA6B,CAF7B,eAAgB,CAChB,eAAiB,CAEjB,aAAc,CACd,iCACF,CAEA,oBAEE,UAAW,CADX,aAAc,CAGd,iBAAkB,CADlB,eAEF,CAEA,cAKE,kBAAmB,CACnB,cAAgB,CAChB,eAAiB,CAEjB,mBAAqB,CALrB,gBAAiB,CAHjB,iBAAkB,CAElB,UAAW,CAKX,wBAAyB,CANzB,QAQF,CAEA,eACE,kBAAgC,CAAhC,+BAAgC,CAEhC,8BAA4C,CAD5C,UAEF,CAEA,YACE,kBAA6B,CAA7B,4BAA6B,CAE7B,8BAA4C,CAD5C,UAEF,CAGA,gBAKE,kBAAmB,CAHnB,YAAa,CACb,qBAAsB,CACtB,sBAAuB,CAEvB,gBAAiB,CAEjB,YAAa,CADb,iBAEF,CAEA,SAME,iCAAkC,CAFlC,wBAA0C,CAC1C,iBAAkB,CADlB,wBAA0C,CAA1C,yCAA0C,CAF1C,WAAY,CAKZ,kBAAmB,CANnB,UAOF,CAEA,gBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAEA,WAEE,aAA2B,CAA3B,0BAA2B,CAD3B,eAAgB,CAEhB,eACF,CAEA,UACE,aAAc,CACd,kBACF,CAEA,SACE,UAAW,CACX,kBACF,CAGA,yBACE,KACE,YACF,CAEA,YACE,YACF,CAEA,eACE,eACF,CAEA,cACE,eAAgB,CAChB,gBACF,CAEA,KAGE,QAAS,CAFT,yBAGF,CAEA,kBAJE,YAMF,CAEA,aACE,eACF,CAEA,6CAEE,eACF,CACF,CAEA,yBACE,eACE,eACF,CAEA,cACE,eACF,CAEA,aACE,YACF,CAEA,gBACE,eACF,CAMA,0BACE,aACF,CAEA,KACE,YACF,CACF,CAGA,kBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,aACE,6BACF,CAGA,uCACE,0BAEE,eACF,CAEA,SACE,cACF,CACF,CAGA,+BACE,MACE,oBAAwB,CACxB,yBAA0B,CAC1B,iBACF,CACF,CAKA,sBAEE,kBAAmB,CADnB,YAAa,CAIb,cAAe,CAFf,QAAS,CAGT,sBAAuB,CAFvB,aAGF,CAEA,aAIE,oBAAoC,CAFpC,oCAAqC,CACrC,kBAAmB,CAEnB,aAA2B,CAA3B,0BAA2B,CAG3B,cAAe,CAFf,cAAe,CACf,eAAgB,CANhB,iBAAkB,CAQlB,uBACF,CAEA,mBACE,eAAiB,CACjB,+BACF,CAEA,mBAEE,8BAA4C,CAD5C,YAEF,CAGA,cACE,kDAAqD,CACrD,wBAAyB,CACzB,kBAAmB,CAOnB,+BAA6C,CAF7C,aAAc,CACd,cAAe,CAJf,gBAAiB,CACjB,eAAgB,CAFhB,iBAAkB,CAGlB,iBAIF,CAEA,qBACE,aACF,CAGA,eACE,kDAAqD,CACrD,wBAAyB,CACzB,kBAAmB,CAMnB,+BAA8C,CAD9C,aAAc,CAHd,gBAAiB,CACjB,eAAgB,CAFhB,YAAa,CAGb,iBAGF,CAEA,kBAEE,aAAc,CACd,cAAe,CAFf,eAGF,CAEA,iBAEE,cAAe,CACf,eAAgB,CAFhB,QAGF,CAEA,sBACE,aAAc,CACd,eACF,CAGA,mBACE,kDAAqD,CACrD,wBAAyB,CACzB,kBAAmB,CAInB,+BAA8C,CAF9C,gBAAiB,CACjB,eAAgB,CAFhB,YAIF,CAEA,sBAEE,aAAc,CAEd,cAAe,CADf,eAAkB,CAFlB,iBAIF,CAEA,gBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,eAGE,kBAAmB,CACnB,gBAAoC,CAGpC,6BAA8B,CAD9B,kBAAmB,CALnB,YAAa,CACb,6BAA8B,CAG9B,iBAAkB,CAGlB,uBACF,CAEA,qBACE,oBAAqC,CAErC,+BAA+C,CAD/C,yBAEF,CAEA,eAEE,aAAc,CACd,QAAO,CAFP,eAGF,CAEA,eACE,aAAc,CACd,cAAe,CACf,eACF,CAGA,yBACE,sBACE,qBAAsB,CACtB,QACF,CAEA,aAEE,eAAgB,CADhB,UAEF,CAEA,eACE,qBAAsB,CAEtB,OAAQ,CADR,iBAEF,CAEA,eACE,iBACF,CAEA,gDAGE,gBAAiB,CACjB,YACF,CACF,CAEA,yBACE,sBACE,cACF,CAEA,eACE,iBACF,CAEA,kBACE,cACF,CAEA,iBACE,cACF,CACF", "sources": ["index.css", "App.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n", ":root {\n  --primary-color: #006400; /* <PERSON> Green */\n  --secondary-color: #DAA520; /* Goldenrod */\n  --background-color: #F0F2F5;\n  --card-background: #FFFFFF;\n  --text-color: #333333;\n  --highlight-color: #FFD700; /* Gold for highlighting */\n  --current-color: #228B22; /* Forest Green */\n  --next-color: #FF8C00; /* Dark Orange */\n  --shadow-color: rgba(0, 0, 0, 0.1);\n}\n\n* {\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n  background: linear-gradient(135deg, #E8F5E8 0%, #F0F8FF 100%);\n  color: var(--text-color);\n  margin: 0;\n  padding: 20px;\n  min-height: 100vh;\n}\n\n.App {\n  max-width: 1200px;\n  margin: 0 auto;\n  background-color: var(--background-color);\n  border-radius: 15px;\n  box-shadow: 0 8px 32px var(--shadow-color);\n  overflow: hidden;\n}\n\n/* Header Styles */\n.App-header {\n  background: linear-gradient(135deg, var(--primary-color) 0%, #228B22 100%);\n  color: white;\n  padding: 30px;\n  text-align: center;\n  position: relative;\n}\n\n.App-header::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"islamic\" patternUnits=\"userSpaceOnUse\" width=\"20\" height=\"20\"><circle cx=\"10\" cy=\"10\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23islamic)\"/></svg>') repeat;\n  opacity: 0.3;\n}\n\n.App-header > * {\n  position: relative;\n  z-index: 1;\n}\n\n.App-header h1 {\n  margin: 0 0 10px 0;\n  font-size: 2.8em;\n  font-weight: 700;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.date {\n  font-size: 1.2em;\n  margin: 10px 0;\n  opacity: 0.9;\n}\n\n.current-time {\n  font-size: 1.8em;\n  font-weight: bold;\n  margin: 15px 0;\n  background: rgba(255, 255, 255, 0.2);\n  padding: 10px 20px;\n  border-radius: 25px;\n  display: inline-block;\n  backdrop-filter: blur(10px);\n}\n\n.refresh-btn {\n  background: var(--secondary-color);\n  color: white;\n  border: none;\n  padding: 12px 24px;\n  border-radius: 25px;\n  font-size: 1em;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  margin-top: 15px;\n  box-shadow: 0 4px 15px rgba(218, 165, 32, 0.3);\n}\n\n.refresh-btn:hover {\n  background: #B8860B;\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(218, 165, 32, 0.4);\n}\n\n/* Prayer Info Sections */\n.current-prayer-info,\n.next-prayer-info {\n  padding: 20px;\n  text-align: center;\n  font-weight: 600;\n}\n\n.current-prayer-info {\n  background: linear-gradient(135deg, rgba(34, 139, 34, 0.1) 0%, rgba(34, 139, 34, 0.05) 100%);\n  color: var(--current-color);\n  border-bottom: 3px solid var(--current-color);\n}\n\n.next-prayer-info {\n  background: linear-gradient(135deg, rgba(255, 140, 0, 0.1) 0%, rgba(255, 140, 0, 0.05) 100%);\n  color: var(--next-color);\n  border-bottom: 3px solid var(--next-color);\n}\n\n.current-prayer-info h3,\n.next-prayer-info h3 {\n  margin: 0;\n  font-size: 1.3em;\n}\n\n/* Main Content */\nmain {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: 25px;\n  padding: 30px;\n  background: var(--background-color);\n}\n\n/* Prayer Cards */\n.prayer-card {\n  background: linear-gradient(145deg, var(--card-background), #FAFAFA);\n  border-radius: 15px;\n  padding: 25px;\n  box-shadow: 0 6px 20px var(--shadow-color);\n  transition: all 0.3s ease;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  text-align: center;\n  border: 2px solid transparent;\n  position: relative;\n  overflow: hidden;\n}\n\n.prayer-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));\n  opacity: 0.7;\n}\n\n.prayer-card:hover {\n  transform: translateY(-8px);\n  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);\n  border-color: var(--primary-color);\n}\n\n.prayer-card.current {\n  border-color: var(--current-color);\n  background: linear-gradient(145deg, #F0FFF0, #F8FFF8);\n  box-shadow: 0 8px 25px rgba(34, 139, 34, 0.2);\n}\n\n.prayer-card.next {\n  border-color: var(--next-color);\n  background: linear-gradient(145deg, #FFF8DC, #FFFAF0);\n  box-shadow: 0 8px 25px rgba(255, 140, 0, 0.2);\n}\n\n.prayer-icon {\n  font-size: 2.5em;\n  margin-bottom: 15px;\n  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));\n}\n\n.prayer-card h2 {\n  color: var(--primary-color);\n  font-size: 1.6em;\n  margin: 0 0 10px 0;\n  font-weight: 700;\n}\n\n.prayer-time {\n  font-size: 2.5em;\n  font-weight: bold;\n  color: var(--secondary-color);\n  margin: 10px 0;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n.prayer-description {\n  font-size: 1em;\n  color: #666;\n  margin: 10px 0 0 0;\n  font-style: italic;\n}\n\n.status-badge {\n  position: absolute;\n  top: 15px;\n  right: 15px;\n  padding: 6px 12px;\n  border-radius: 20px;\n  font-size: 0.8em;\n  font-weight: bold;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.current-badge {\n  background: var(--current-color);\n  color: white;\n  box-shadow: 0 2px 8px rgba(34, 139, 34, 0.3);\n}\n\n.next-badge {\n  background: var(--next-color);\n  color: white;\n  box-shadow: 0 2px 8px rgba(255, 140, 0, 0.3);\n}\n\n/* Loading and Error States */\n.loading,\n.error {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  min-height: 400px;\n  text-align: center;\n  padding: 40px;\n}\n\n.spinner {\n  width: 50px;\n  height: 50px;\n  border: 4px solid #f3f3f3;\n  border-top: 4px solid var(--primary-color);\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 20px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading p {\n  font-size: 1.2em;\n  color: var(--primary-color);\n  font-weight: 600;\n}\n\n.error h2 {\n  color: #dc3545;\n  margin-bottom: 15px;\n}\n\n.error p {\n  color: #666;\n  margin-bottom: 20px;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  body {\n    padding: 10px;\n  }\n\n  .App-header {\n    padding: 20px;\n  }\n\n  .App-header h1 {\n    font-size: 2.2em;\n  }\n\n  .current-time {\n    font-size: 1.5em;\n    padding: 8px 16px;\n  }\n\n  main {\n    grid-template-columns: 1fr;\n    padding: 20px;\n    gap: 20px;\n  }\n\n  .prayer-card {\n    padding: 20px;\n  }\n\n  .prayer-time {\n    font-size: 2.2em;\n  }\n\n  .current-prayer-info h3,\n  .next-prayer-info h3 {\n    font-size: 1.1em;\n  }\n}\n\n@media (max-width: 480px) {\n  .App-header h1 {\n    font-size: 1.8em;\n  }\n\n  .current-time {\n    font-size: 1.3em;\n  }\n\n  .prayer-card {\n    padding: 15px;\n  }\n\n  .prayer-card h2 {\n    font-size: 1.4em;\n  }\n\n  .prayer-time {\n    font-size: 2em;\n  }\n\n  .prayer-icon {\n    font-size: 2em;\n  }\n\n  main {\n    padding: 15px;\n  }\n}\n\n/* Animation for smooth transitions */\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.prayer-card {\n  animation: fadeIn 0.6s ease-out;\n}\n\n/* Accessibility improvements */\n@media (prefers-reduced-motion: reduce) {\n  .prayer-card,\n  .refresh-btn {\n    transition: none;\n  }\n  \n  .spinner {\n    animation: none;\n  }\n}\n\n/* High contrast mode support */\n@media (prefers-contrast: high) {\n  :root {\n    --primary-color: #000080;\n    --secondary-color: #8B4513;\n    --text-color: #000000;\n  }\n}\n\n\n\n/* Time and Date Picker Container */\n.time-and-date-picker {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  margin: 15px 0;\n  flex-wrap: wrap;\n  justify-content: center;\n}\n\n.date-picker {\n  padding: 10px 15px;\n  border: 2px solid var(--accent-color);\n  border-radius: 25px;\n  background: rgba(255, 255, 255, 0.9);\n  color: var(--primary-color);\n  font-size: 16px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.date-picker:hover {\n  background: white;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\n}\n\n.date-picker:focus {\n  outline: none;\n  box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.3);\n}\n\n/* Shenime Info */\n.shenime-info {\n  background: linear-gradient(135deg, #fff3cd, #ffeaa7);\n  border: 2px solid #ffc107;\n  border-radius: 15px;\n  padding: 15px 20px;\n  margin: 20px auto;\n  max-width: 800px;\n  text-align: center;\n  color: #856404;\n  font-size: 16px;\n  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);\n}\n\n.shenime-info strong {\n  color: #b8860b;\n}\n\n/* Daylight Info */\n.daylight-info {\n  background: linear-gradient(135deg, #e3f2fd, #bbdefb);\n  border: 2px solid #2196f3;\n  border-radius: 15px;\n  padding: 20px;\n  margin: 30px auto;\n  max-width: 600px;\n  text-align: center;\n  color: #0d47a1;\n  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.2);\n}\n\n.daylight-info h3 {\n  margin: 0 0 10px 0;\n  color: #1565c0;\n  font-size: 20px;\n}\n\n.daylight-info p {\n  margin: 0;\n  font-size: 16px;\n  line-height: 1.5;\n}\n\n.daylight-info strong {\n  color: #0d47a1;\n  font-weight: 700;\n}\n\n/* Festivals Section */\n.festivals-section {\n  background: linear-gradient(135deg, #f3e5f5, #e1bee7);\n  border: 2px solid #9c27b0;\n  border-radius: 15px;\n  padding: 25px;\n  margin: 30px auto;\n  max-width: 800px;\n  box-shadow: 0 4px 15px rgba(156, 39, 176, 0.2);\n}\n\n.festivals-section h3 {\n  text-align: center;\n  color: #4a148c;\n  margin: 0 0 20px 0;\n  font-size: 22px;\n}\n\n.festivals-list {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.festival-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: rgba(255, 255, 255, 0.8);\n  padding: 15px 20px;\n  border-radius: 10px;\n  border-left: 4px solid #9c27b0;\n  transition: all 0.3s ease;\n}\n\n.festival-item:hover {\n  background: rgba(255, 255, 255, 0.95);\n  transform: translateX(5px);\n  box-shadow: 0 2px 10px rgba(156, 39, 176, 0.15);\n}\n\n.festival-name {\n  font-weight: 600;\n  color: #4a148c;\n  flex: 1;\n}\n\n.festival-date {\n  color: #7b1fa2;\n  font-size: 14px;\n  font-weight: 500;\n}\n\n/* Responsive Design for New Elements */\n@media (max-width: 768px) {\n  .time-and-date-picker {\n    flex-direction: column;\n    gap: 15px;\n  }\n  \n  .date-picker {\n    width: 100%;\n    max-width: 250px;\n  }\n  \n  .festival-item {\n    flex-direction: column;\n    text-align: center;\n    gap: 8px;\n  }\n  \n  .festival-name {\n    margin-bottom: 5px;\n  }\n  \n  .daylight-info,\n  .festivals-section,\n  .shenime-info {\n    margin: 20px 10px;\n    padding: 15px;\n  }\n}\n\n@media (max-width: 480px) {\n  .festivals-section h3 {\n    font-size: 18px;\n  }\n  \n  .festival-item {\n    padding: 12px 15px;\n  }\n  \n  .daylight-info h3 {\n    font-size: 18px;\n  }\n  \n  .daylight-info p {\n    font-size: 14px;\n  }\n}\n\n"], "names": [], "sourceRoot": ""}