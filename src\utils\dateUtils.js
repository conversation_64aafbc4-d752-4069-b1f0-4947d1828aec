// Albanian date formatting utilities

export const albanianMonths = {
  1: 'janar',
  2: 'shkurt', 
  3: 'mars',
  4: 'prill',
  5: 'maj',
  6: 'qershor',
  7: 'korrik',
  8: 'gusht',
  9: 'shtator',
  10: 'tetor',
  11: 'në<PERSON><PERSON>',
  12: 'dhjetor'
};

export const albanianDays = {
  0: 'E diel',
  1: 'E hënë',
  2: 'E martë',
  3: 'E mërkurë',
  4: 'E enjte',
  5: 'E premte',
  6: 'E shtunë'
};

export const formatDateToAlbanian = (date) => {
  const dayOfWeek = albanianDays[date.getDay()];
  const day = date.getDate();
  const month = albanianMonths[date.getMonth() + 1];
  const year = date.getFullYear();
  
  return `${dayOfWeek}, ${day} ${month} ${year}`;
};

export const formatDateForAPI = (date) => {
  const day = date.getDate();
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
                     'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const month = monthNames[date.getMonth()];
  
  return `${day}-${month}`;
};

export const calculateRemainingTime = (targetTime, currentTime) => {
  // Parse target time (HH:MM format)
  const [targetHours, targetMinutes] = targetTime.split(':').map(Number);
  
  // Create target date with today's date
  const target = new Date(currentTime);
  target.setHours(targetHours, targetMinutes, 0, 0);
  
  // If target time has passed today, it's for tomorrow
  if (target <= currentTime) {
    target.setDate(target.getDate() + 1);
  }
  
  const diff = target - currentTime;
  
  if (diff <= 0) return '00:00:00';
  
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((diff % (1000 * 60)) / 1000);
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

export const calculateDaylightTime = (sunrise, sunset) => {
  const [sunriseHours, sunriseMinutes] = sunrise.split(':').map(Number);
  const [sunsetHours, sunsetMinutes] = sunset.split(':').map(Number);
  
  const sunriseInMinutes = sunriseHours * 60 + sunriseMinutes;
  const sunsetInMinutes = sunsetHours * 60 + sunsetMinutes;
  
  const daylightMinutes = sunsetInMinutes - sunriseInMinutes;
  
  const hours = Math.floor(daylightMinutes / 60);
  const minutes = daylightMinutes % 60;
  
  return `${hours} orë dhe ${minutes} minuta`;
};

